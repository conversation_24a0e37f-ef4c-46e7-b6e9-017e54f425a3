# Exercise 3: Test Inheritance and Organization

## Overview

This exercise demonstrates how to use inheritance in test organization to eliminate code duplication and create better test structure. We create a base test class with common setup and teardown logic, and then create specific test classes that inherit from it.

## Learning Objectives

- Understand inheritance in object-oriented programming
- Learn how to organize tests using base classes
- Eliminate code duplication in test setup and teardown
- Use protected access modifiers effectively
- Implement inheritance-based test architecture

## Prerequisites

### Required Software
- .NET 8.0 SDK or later
- Visual Studio Code or Visual Studio
- Git

## Project Structure

```
exercise-3-calculator/
├── Calculators/                    # Calculator library (same as Exercise 2)
│   ├── SimpleCalculator.cs
│   ├── ContentSafety/
│   ├── Demo/
│   └── Testing/
├── Exercise3Tests/                 # Test project with inheritance
│   ├── BaseTest.cs                # Base class with common setup/teardown
│   ├── ArithmeticOperationsTests.cs # Inherits from BaseTest
│   └── NegativeNumberTests.cs     # Inherits from BaseTest
├── SafetyDemo/                     # Demo application
└── README.md
```

## Key Concepts Demonstrated

### 1. Inheritance in Testing

**Base Class (BaseTest)**:
- Contains common setup and teardown logic
- Declares protected properties accessible to subclasses
- Uses `[SetUp]` and `[TearDown]` attributes
- Provides shared functionality for all test classes

**Derived Classes**:
- Inherit from BaseTest using `: BaseTest` syntax
- Automatically get setup and teardown behavior
- Can access protected members from base class
- Focus on specific test scenarios

### 2. Access Modifiers

- **Protected**: Members accessible to the class and its subclasses
- **Private**: Members accessible only within the same class
- **Public**: Members accessible from anywhere

### 3. Code Organization Benefits

- **DRY Principle**: Don't Repeat Yourself - common code in one place
- **Maintainability**: Changes to setup logic only need to be made in one place
- **Consistency**: All test classes use the same setup/teardown pattern
- **Specialization**: Each test class focuses on specific scenarios

## Running the Tests

### Build the Solution
```bash
dotnet build
```

### Run All Tests
```bash
dotnet test
```

### Run Tests with Detailed Output
```bash
dotnet test --logger "console;verbosity=detailed"
```

## Success Criteria

✅ BaseTest class created with protected Calculator property  
✅ Setup method initializes calculator with value 4  
✅ Teardown method resets calculator after each test  
✅ ArithmeticOperationsTests inherits from BaseTest  
✅ NegativeNumberTests inherits from BaseTest  
✅ All tests use the inherited Calculator property  
✅ All tests pass when run individually and together  
✅ Test output shows BaseTest setup/teardown execution  
✅ No duplicate setup/teardown code in derived classes
