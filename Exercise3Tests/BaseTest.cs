using Calculators;
using NUnit.Framework;

namespace Exercise3Tests
{
    /// <summary>
    /// Base test class containing common setup and teardown logic for calculator tests.
    /// This demonstrates inheritance in test organization - common functionality is placed
    /// in the base class and specific test classes inherit from it.
    /// </summary>
    public class BaseTest
    {
        /// <summary>
        /// Protected SimpleCalculator property that can be accessed by inheriting test classes.
        /// Using protected access modifier allows subclasses to use this property while
        /// keeping it hidden from external classes.
        /// </summary>
        protected SimpleCalculator Calculator { get; private set; }

        /// <summary>
        /// Setup method that runs before each test in any inheriting class.
        /// Initializes the calculator with the number 4 as the starting value.
        /// </summary>
        [SetUp]
        public void Setup()
        {
            TestContext.Progress.WriteLine("🔧 BaseTest Setup: Creating calculator and entering initial value 4");
            Calculator = new SimpleCalculator();
            Calculator.Enter(4);
        }

        /// <summary>
        /// Teardown method that runs after each test in any inheriting class.
        /// Resets the calculator to ensure test isolation.
        /// </summary>
        [TearDown]
        public void TearDown()
        {
            TestContext.Progress.WriteLine("🧹 BaseTest TearDown: Resetting calculator");
            Calculator.Reset();
        }
    }
}
