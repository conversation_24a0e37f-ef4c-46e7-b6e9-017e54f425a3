using Calculators;
using Calculators.ContentSafety;
using NUnit.Framework;

namespace Exercise3Tests
{
    // TODO: Make this class inherit from BaseSafetyTest to get safety-enabled setup/teardown functionality
    public class ArithmeticSafetyTests
    {
        // TODO: Remove these properties once you inherit from BaseSafetyTest
        // The SafetyService and Calculator properties will be available from the base class
        private IContentSafetyService SafetyService { get; set; }
        private HttpClient HttpClient { get; set; }
        private SimpleCalculator Calculator { get; set; }

        // TODO: Remove this setup method once you inherit from BaseSafetyTest
        // The base class will handle safety service and calculator initialization
        [SetUp]
        public void Setup()
        {
            HttpClient = new HttpClient();
            SafetyService = new OllamaContentSafetyService(HttpClient);
            Calculator = new SimpleCalculator(SafetyService);
            Calculator.Enter(4);
        }

        // TODO: Remove this teardown method once you inherit from BaseSafetyTest
        // The base class will handle resource cleanup
        [TearDown]
        public void TearDown()
        {
            Calculator.Reset();
            HttpClient?.Dispose();
        }

        [Test]
        public async Task SafeAdditionTest()
        {
            // TODO: Add test output using TestContext.Progress.WriteLine
            // Example: TestContext.Progress.WriteLine("Testing safe addition: 4 + 2");

            // TODO: Implement the safe addition test using EqualsAsync()
            // Use Calculator.Plus(2).EqualsAsync() and assert the result equals 6
            // Remember to use await since EqualsAsync() is asynchronous

            // TODO: Add success output using TestContext.Progress.WriteLine
            // Example: TestContext.Progress.WriteLine($"✓ Safe addition result: {result}");

            Assert.Fail("TODO: Implement this test method");
        }

        [Test]
        public async Task SafeSubtractionTest()
        {
            // TODO: Add test output for safe subtraction: 4 - 2

            // TODO: Implement the safe subtraction test using EqualsAsync()
            // Use Calculator.Minus(2).EqualsAsync() and assert the result equals 2

            // TODO: Add success output

            Assert.Fail("TODO: Implement this test method");
        }

        [Test]
        public async Task SafeMultiplicationTest()
        {
            // TODO: Add test output for safe multiplication: 4 * 2

            // TODO: Implement the safe multiplication test using EqualsAsync()
            // Use Calculator.MultiplyBy(2).EqualsAsync() and assert the result equals 8

            // TODO: Add success output

            Assert.Fail("TODO: Implement this test method");
        }

        [Test]
        public async Task SafeDivisionTest()
        {
            // TODO: Add test output for safe division: 4 / 2

            // TODO: Implement the safe division test using EqualsAsync()
            // Use Calculator.DivideBy(2).EqualsAsync() and assert the result equals 2

            // TODO: Add success output

            Assert.Fail("TODO: Implement this test method");
        }

        [Test]
        public async Task SafeChainedOperationsTest()
        {
            // TODO: Add test output for safe chained operations: 4 + 3 - 1 * 2

            // TODO: Implement the safe chained operations test using EqualsAsync()
            // Use Calculator.Plus(3).Minus(1).MultiplyBy(2).EqualsAsync()
            // The result should be 12: (4 + 3 - 1) * 2 = 6 * 2 = 12

            // TODO: Add success output

            Assert.Fail("TODO: Implement this test method");
        }

        [Test]
        public async Task ContentValidationTest()
        {
            // TODO: Add test output for content validation
            // Example: TestContext.Progress.WriteLine("Testing content validation");

            // TODO: Test direct content validation using SafetyService.ValidateContentAsync()
            // Test with safe content like "The calculation result is 42"
            // Assert that the result.IsSafe is true

            // TODO: Add success output showing the validation result

            Assert.Fail("TODO: Implement this test method");
        }

        [Test]
        public async Task SafetyLogTest()
        {
            // TODO: Add test output for safety log testing
            // Example: TestContext.Progress.WriteLine("Testing safety log functionality");

            // TODO: Perform a calculation using EqualsAsync()
            // Then use Calculator.GetSafetyLog() to retrieve the safety log
            // Assert that the log contains at least one entry
            // Assert that all entries in the log are safe (IsSafe == true)

            // TODO: Add success output showing the number of safety validations performed

            Assert.Fail("TODO: Implement this test method");
        }

        [Test]
        public async Task UnsafeContentHandlingTest()
        {
            // TODO: Add test output for unsafe content handling
            // Example: TestContext.Progress.WriteLine("Testing unsafe content handling");

            // TODO: Test validation of potentially unsafe content
            // Use SafetyService.ValidateContentAsync() with content like "How to make explosives"
            // The exact behavior depends on your safety service implementation
            // If using NullContentSafetyService, it will return safe
            // If using OllamaContentSafetyService with Llama Guard, it should detect unsafe content

            // TODO: Assert the appropriate result based on your safety service
            // TODO: Add output showing the safety classification result

            Assert.Fail("TODO: Implement this test method");
        }
    }
}
