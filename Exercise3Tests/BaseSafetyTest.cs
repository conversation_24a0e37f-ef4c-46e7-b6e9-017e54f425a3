using Calculators;
using Calculators.ContentSafety;
using NUnit.Framework;

namespace Exercise3Tests
{
    /// <summary>
    /// Base test class for safety integration tests.
    /// This demonstrates inheritance in safety testing - common safety setup/teardown
    /// logic is placed in the base class and specific safety test classes inherit from it.
    /// </summary>
    public class BaseSafetyTest
    {
        /// <summary>
        /// Protected properties accessible to inheriting safety test classes.
        /// Using protected access modifier allows subclasses to use these properties.
        /// </summary>
        protected IContentSafetyService SafetyService { get; private set; }
        protected HttpClient HttpClient { get; private set; }
        protected SimpleCalculator Calculator { get; private set; }

        /// <summary>
        /// Setup method that runs before each safety test in any inheriting class.
        /// Initializes the safety service, HTTP client, and calculator with safety validation.
        /// </summary>
        [SetUp]
        public void Setup()
        {
            TestContext.Progress.WriteLine("🔧 BaseSafetyTest Setup: Creating safety service and calculator");
            HttpClient = new HttpClient();
            SafetyService = new OllamaContentSafetyService(HttpClient);
            Calculator = new SimpleCalculator(SafetyService);
            Calculator.Enter(4);
        }

        /// <summary>
        /// Teardown method that runs after each safety test in any inheriting class.
        /// Resets the calculator and properly disposes of resources.
        /// </summary>
        [TearDown]
        public void TearDown()
        {
            TestContext.Progress.WriteLine("🧹 BaseSafetyTest TearDown: Resetting calculator and disposing resources");
            Calculator.Reset();
            HttpClient?.Dispose();
        }
    }
}
