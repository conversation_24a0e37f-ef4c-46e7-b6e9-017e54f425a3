using Calculators;
using NUnit.Framework;

namespace Exercise3Tests
{
    // TODO: Make this class inherit from BaseTest to get common setup/teardown functionality
    public class ArithmeticOperationsTests
    {
        // TODO: Remove this property once you inherit from BaseTest
        // The Calculator property will be available from the base class
        private SimpleCalculator Calculator { get; set; }

        // TODO: Remove this setup method once you inherit from BaseTest
        // The base class will handle calculator initialization
        [SetUp]
        public void Setup()
        {
            Calculator = new SimpleCalculator();
            Calculator.Enter(4);
        }

        [Test]
        public void AdditionTest()
        {
            // TODO: Add test output using TestContext.Progress.WriteLine
            // Example: TestContext.Progress.WriteLine("Testing addition: 4 + 2");

            // TODO: Implement the addition test
            // Use Calculator.Plus(2).Equals() and assert the result equals 6

            // TODO: Add success output using TestContext.Progress.WriteLine
            // Example: TestContext.Progress.WriteLine($"✓ Addition result: {result}");

            Assert.Fail("TODO: Implement this test method");
        }

        [Test]
        public void SubtractionTest()
        {
            // TODO: Add test output for subtraction: 4 - 2

            // TODO: Implement the subtraction test
            // Use Calculator.Minus(2).Equals() and assert the result equals 2

            // TODO: Add success output

            Assert.Fail("TODO: Implement this test method");
        }

        [Test]
        public void MultiplicationTest()
        {
            // TODO: Add test output for multiplication: 4 * 2

            // TODO: Implement the multiplication test
            // Use Calculator.MultiplyBy(2).Equals() and assert the result equals 8

            // TODO: Add success output

            Assert.Fail("TODO: Implement this test method");
        }

        [Test]
        public void DivisionTest()
        {
            // TODO: Add test output for division: 4 / 2

            // TODO: Implement the division test
            // Use Calculator.DivideBy(2).Equals() and assert the result equals 2

            // TODO: Add success output

            Assert.Fail("TODO: Implement this test method");
        }

        [Test]
        public void ChainedOperationsTest()
        {
            // TODO: Add test output for chained operations: 4 + 3 - 1 * 2

            // TODO: Implement the chained operations test
            // Use Calculator.Plus(3).Minus(1).MultiplyBy(2).Equals()
            // The result should be 12: (4 + 3 - 1) * 2 = 6 * 2 = 12

            // TODO: Add success output

            Assert.Fail("TODO: Implement this test method");
        }
    }
}
