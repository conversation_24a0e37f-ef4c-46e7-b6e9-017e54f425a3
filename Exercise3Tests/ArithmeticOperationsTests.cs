using NUnit.Framework;

namespace Exercise3Tests
{
    /// <summary>
    /// Test class for basic arithmetic operations.
    /// Inherits from BaseTest to get common setup/teardown functionality.
    /// This demonstrates inheritance in test organization - the base class handles
    /// calculator initialization and cleanup, while this class focuses on specific tests.
    /// </summary>
    public class ArithmeticOperationsTests : BaseTest
    {
        [Test]
        public void AdditionTest()
        {
            // Arrange - calculator already has 4 from BaseTest setup
            TestContext.Progress.WriteLine("Testing addition: 4 + 2");
            
            // Act
            var result = Calculator.Plus(2).Equals();
            
            // Assert
            Assert.That(result, Is.EqualTo(6));
            TestContext.Progress.WriteLine($"✓ Addition result: {result}");
        }

        [Test]
        public void SubtractionTest()
        {
            // Arrange - calculator already has 4 from BaseTest setup
            TestContext.Progress.WriteLine("Testing subtraction: 4 - 2");
            
            // Act
            var result = Calculator.Minus(2).Equals();
            
            // Assert
            Assert.That(result, Is.EqualTo(2));
            TestContext.Progress.WriteLine($"✓ Subtraction result: {result}");
        }

        [Test]
        public void MultiplicationTest()
        {
            // Arrange - calculator already has 4 from BaseTest setup
            TestContext.Progress.WriteLine("Testing multiplication: 4 * 2");
            
            // Act
            var result = Calculator.MultiplyBy(2).Equals();
            
            // Assert
            Assert.That(result, Is.EqualTo(8));
            TestContext.Progress.WriteLine($"✓ Multiplication result: {result}");
        }

        [Test]
        public void DivisionTest()
        {
            // Arrange - calculator already has 4 from BaseTest setup
            TestContext.Progress.WriteLine("Testing division: 4 / 2");
            
            // Act
            var result = Calculator.DivideBy(2).Equals();
            
            // Assert
            Assert.That(result, Is.EqualTo(2));
            TestContext.Progress.WriteLine($"✓ Division result: {result}");
        }

        [Test]
        public void ChainedOperationsTest()
        {
            // Arrange - calculator already has 4 from BaseTest setup
            TestContext.Progress.WriteLine("Testing chained operations: 4 + 3 - 1 * 2");
            
            // Act
            var result = Calculator.Plus(3).Minus(1).MultiplyBy(2).Equals();
            
            // Assert
            Assert.That(result, Is.EqualTo(12)); // (4 + 3 - 1) * 2 = 6 * 2 = 12
            TestContext.Progress.WriteLine($"✓ Chained operations result: {result}");
        }
    }
}
