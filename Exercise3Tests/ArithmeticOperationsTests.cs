using NUnit.Framework;

namespace Exercise3Tests
{
    public class ArithmeticOperationsTests : BaseTest
    {
        [Test]
        public void AdditionTest()
        {
            TestContext.Progress.WriteLine("Testing addition: 4 + 2");

            var result = Calculator.Plus(2).Equals();

            Assert.That(result, Is.EqualTo(6));
            TestContext.Progress.WriteLine($"✓ Addition result: {result}");
        }

        [Test]
        public void SubtractionTest()
        {
            TestContext.Progress.WriteLine("Testing subtraction: 4 - 2");

            var result = Calculator.Minus(2).Equals();

            Assert.That(result, Is.EqualTo(2));
            TestContext.Progress.WriteLine($"✓ Subtraction result: {result}");
        }

        [Test]
        public void MultiplicationTest()
        {
            TestContext.Progress.WriteLine("Testing multiplication: 4 * 2");

            var result = Calculator.MultiplyBy(2).Equals();

            Assert.That(result, Is.EqualTo(8));
            TestContext.Progress.WriteLine($"✓ Multiplication result: {result}");
        }

        [Test]
        public void DivisionTest()
        {
            TestContext.Progress.WriteLine("Testing division: 4 / 2");

            var result = Calculator.DivideBy(2).Equals();

            Assert.That(result, Is.EqualTo(2));
            TestContext.Progress.WriteLine($"✓ Division result: {result}");
        }

        [Test]
        public void ChainedOperationsTest()
        {
            TestContext.Progress.WriteLine("Testing chained operations: 4 + 3 - 1 * 2");

            var result = Calculator.Plus(3).Minus(1).MultiplyBy(2).Equals();

            Assert.That(result, Is.EqualTo(12));
            TestContext.Progress.WriteLine($"✓ Chained operations result: {result}");
        }
    }
}
