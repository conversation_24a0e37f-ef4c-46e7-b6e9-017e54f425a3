using NUnit.Framework;

namespace Exercise3Tests
{
    /// <summary>
    /// Test class for operations involving negative numbers.
    /// Inherits from BaseTest to get common setup/teardown functionality.
    /// This class demonstrates inheritance by reusing the base setup while
    /// focusing on specific test scenarios with negative numbers.
    /// </summary>
    public class NegativeNumberTests : BaseTest
    {
        [Test]
        public void AddNegativeNumberTest()
        {
            // Arrange - calculator already has 4 from BaseTest setup
            TestContext.Progress.WriteLine("Testing addition with negative number: 4 + (-3)");
            
            // Act
            var result = Calculator.Plus(-3).Equals();
            
            // Assert
            Assert.That(result, Is.EqualTo(1));
            TestContext.Progress.WriteLine($"✓ Addition with negative result: {result}");
        }

        [Test]
        public void SubtractNegativeNumberTest()
        {
            // Arrange - calculator already has 4 from BaseTest setup
            TestContext.Progress.WriteLine("Testing subtraction with negative number: 4 - (-2)");
            
            // Act
            var result = Calculator.Minus(-2).Equals();
            
            // Assert
            Assert.That(result, Is.EqualTo(6)); // 4 - (-2) = 4 + 2 = 6
            TestContext.Progress.WriteLine($"✓ Subtraction with negative result: {result}");
        }

        [Test]
        public void MultiplyByNegativeNumberTest()
        {
            // Arrange - calculator already has 4 from BaseTest setup
            TestContext.Progress.WriteLine("Testing multiplication with negative number: 4 * (-2)");
            
            // Act
            var result = Calculator.MultiplyBy(-2).Equals();
            
            // Assert
            Assert.That(result, Is.EqualTo(-8));
            TestContext.Progress.WriteLine($"✓ Multiplication with negative result: {result}");
        }

        [Test]
        public void DivideByNegativeNumberTest()
        {
            // Arrange - calculator already has 4 from BaseTest setup
            TestContext.Progress.WriteLine("Testing division with negative number: 4 / (-2)");
            
            // Act
            var result = Calculator.DivideBy(-2).Equals();
            
            // Assert
            Assert.That(result, Is.EqualTo(-2));
            TestContext.Progress.WriteLine($"✓ Division with negative result: {result}");
        }

        [Test]
        public void EnterNegativeNumberTest()
        {
            // Arrange - calculator already has 4 from BaseTest setup
            TestContext.Progress.WriteLine("Testing entering negative number: Enter(-5)");
            
            // Act
            var result = Calculator.Enter(-5).Equals();
            
            // Assert
            Assert.That(result, Is.EqualTo(-5));
            TestContext.Progress.WriteLine($"✓ Enter negative number result: {result}");
        }

        [Test]
        public void ChainedNegativeOperationsTest()
        {
            // Arrange - calculator already has 4 from BaseTest setup
            TestContext.Progress.WriteLine("Testing chained negative operations: 4 + (-1) * (-3) - (-2)");
            
            // Act
            var result = Calculator.Plus(-1).MultiplyBy(-3).Minus(-2).Equals();
            
            // Assert
            Assert.That(result, Is.EqualTo(-7)); // (4 + (-1)) * (-3) - (-2) = 3 * (-3) + 2 = -9 + 2 = -7
            TestContext.Progress.WriteLine($"✓ Chained negative operations result: {result}");
        }

        [Test]
        public void NegativeToPositiveTest()
        {
            // Arrange - Start with a negative number instead of the base setup value
            TestContext.Progress.WriteLine("Testing negative to positive: Enter(-6) + 10");
            
            // Act
            var result = Calculator.Enter(-6).Plus(10).Equals();
            
            // Assert
            Assert.That(result, Is.EqualTo(4));
            TestContext.Progress.WriteLine($"✓ Negative to positive result: {result}");
        }

        [Test]
        public void DoubleNegativeTest()
        {
            // Arrange - calculator already has 4 from BaseTest setup
            TestContext.Progress.WriteLine("Testing double negative: 4 - (-(-3))");
            
            // Act - Subtracting a negative of a negative: 4 - (-(-3)) = 4 - 3 = 1
            var result = Calculator.Minus(-(-3)).Equals();
            
            // Assert
            Assert.That(result, Is.EqualTo(1));
            TestContext.Progress.WriteLine($"✓ Double negative result: {result}");
        }
    }
}
