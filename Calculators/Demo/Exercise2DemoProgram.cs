using Calculators.ContentSafety;
using Calculators.Testing;

namespace Calculators.Demo
{
    /// <summary>
    /// Exercise 2 demonstration showing setup/teardown patterns with content safety
    /// </summary>
    public class Exercise2DemoProgram
    {
        private readonly IContentSafetyService _safetyService;
        private SimpleCalculator _calculator;

        public Exercise2DemoProgram(IContentSafetyService safetyService)
        {
            _safetyService = safetyService;
        }

        /// <summary>
        /// Demonstrates setup/teardown pattern similar to NUnit tests
        /// </summary>
        public async Task RunDemoAsync()
        {
            Console.WriteLine("=== Exercise 2: Setup/Teardown Pattern Demo ===\n");

            await DemonstrateSetupTeardownPattern();
            await DemonstrateMultipleOperationsWithReset();
            await DemonstrateContentValidation();

            Console.WriteLine("\n=== Exercise 2 Demo Complete ===");
        }

        /// <summary>
        /// Simulates NUnit setup/teardown pattern
        /// </summary>
        private void Setup()
        {
            Console.WriteLine("🔧 Setup: Creating calculator and entering initial value 4");
            _calculator = new SimpleCalculator();
            _calculator.Enter(4);
        }

        /// <summary>
        /// Simulates NUnit teardown pattern
        /// </summary>
        private void TearDown()
        {
            Console.WriteLine("🧹 TearDown: Resetting calculator");
            _calculator.Reset();
        }

        private async Task DemonstrateSetupTeardownPattern()
        {
            Console.WriteLine("1. Setup/Teardown Pattern Demonstration:");
            Console.WriteLine("---------------------------------------");

            // Test 1: Addition (like NUnit test)
            Setup();
            Console.WriteLine("Test 1 - Addition: Calculator has 4, adding 2");
            var result1 = _calculator.Plus(2).Equals();
            Console.WriteLine($"✓ Result: {result1} (Expected: 6)");
            TearDown();
            Console.WriteLine();

            // Test 2: Subtraction (like NUnit test)
            Setup();
            Console.WriteLine("Test 2 - Subtraction: Calculator has 4, subtracting 2");
            var result2 = _calculator.Minus(2).Equals();
            Console.WriteLine($"✓ Result: {result2} (Expected: 2)");
            TearDown();
            Console.WriteLine();

            // Test 3: Multiplication (like NUnit test)
            Setup();
            Console.WriteLine("Test 3 - Multiplication: Calculator has 4, multiplying by 2");
            var result3 = _calculator.MultiplyBy(2).Equals();
            Console.WriteLine($"✓ Result: {result3} (Expected: 8)");
            TearDown();
            Console.WriteLine();

            // Test 4: Division (like NUnit test)
            Setup();
            Console.WriteLine("Test 4 - Division: Calculator has 4, dividing by 2");
            var result4 = _calculator.DivideBy(2).Equals();
            Console.WriteLine($"✓ Result: {result4} (Expected: 2)");
            TearDown();
            Console.WriteLine();
        }

        private async Task DemonstrateMultipleOperationsWithReset()
        {
            Console.WriteLine("2. Multiple Operations with Reset (Setup/Teardown Benefits):");
            Console.WriteLine("-----------------------------------------------------------");

            Setup();
            Console.WriteLine("Operation 1: 4 + 3 = ?");
            var op1 = _calculator.Plus(3).Equals();
            Console.WriteLine($"✓ Result: {op1}");
            TearDown();

            Setup(); // Fresh start with 4
            Console.WriteLine("Operation 2: 4 * 5 = ? (Fresh calculator with 4)");
            var op2 = _calculator.MultiplyBy(5).Equals();
            Console.WriteLine($"✓ Result: {op2}");
            TearDown();

            Setup(); // Fresh start with 4 again
            Console.WriteLine("Operation 3: 4 - 1 = ? (Fresh calculator with 4)");
            var op3 = _calculator.Minus(1).Equals();
            Console.WriteLine($"✓ Result: {op3}");
            TearDown();

            Console.WriteLine("\nNote: Each operation starts with a fresh calculator containing 4");
            Console.WriteLine("This demonstrates test isolation - each test is independent!\n");
        }

        private async Task DemonstrateContentValidation()
        {
            Console.WriteLine("3. Content Safety with Setup/Teardown (Students will implement):");
            Console.WriteLine("----------------------------------------------------------------");

            Console.WriteLine("TODO: Students will implement safety validation with setup/teardown");
            Console.WriteLine("Expected implementation:");
            Console.WriteLine("- Setup: Create calculator with safety service and enter 4");
            Console.WriteLine("- Test: Perform calculation with EqualsAsync()");
            Console.WriteLine("- TearDown: Reset calculator");
            Console.WriteLine("- Verify: Safety log contains validation results");

            // Direct content validation (working example)
            Console.WriteLine("\nDirect Content Safety Validation (working example):");
            var testContents = new[]
            {
                "The calculation result is 6",
                "Mathematical result: 8",
                "The answer is 2"
            };

            foreach (var content in testContents)
            {
                var classification = await _safetyService.ValidateContentAsync(content);
                var status = classification.IsSafe ? "✓ SAFE" : "✗ UNSAFE";
                Console.WriteLine($"{status}: \"{content}\"");
            }
            Console.WriteLine();
        }

        /// <summary>
        /// Creates and runs a demo instance
        /// </summary>
        public static async Task RunAsync()
        {
            using var httpClient = new HttpClient();
            var safetyService = new OllamaContentSafetyService(httpClient);
            var demo = new Exercise2DemoProgram(safetyService);
            
            await demo.RunDemoAsync();
        }
    }
}
