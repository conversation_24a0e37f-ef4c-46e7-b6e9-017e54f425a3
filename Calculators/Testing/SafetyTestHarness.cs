using Calculators.ContentSafety;

namespace Calculators.Testing
{
    /// <summary>
    /// Test harness for validating content safety classification
    /// </summary>
    public class SafetyTestHarness
    {
        private readonly IContentSafetyService _safetyService;
        private readonly List<TestCase> _testCases;

        public SafetyTestHarness(IContentSafetyService safetyService)
        {
            _safetyService = safetyService;
            _testCases = new List<TestCase>();
        }

        /// <summary>
        /// Adds a test case to the harness
        /// </summary>
        public SafetyTestHarness AddTestCase(string content, bool expectedSafe, string description = "")
        {
            _testCases.Add(new TestCase
            {
                Content = content,
                ExpectedSafe = expectedSafe,
                Description = description
            });
            return this;
        }

        /// <summary>
        /// Runs all test cases and returns the results
        /// </summary>
        public async Task<TestHarnessResult> RunAllTestsAsync()
        {
            var results = new List<TestResult>();
            
            foreach (var testCase in _testCases)
            {
                var classification = await _safetyService.ValidateContentAsync(testCase.Content);
                var passed = classification.IsSafe == testCase.ExpectedSafe;
                
                results.Add(new TestResult
                {
                    TestCase = testCase,
                    Classification = classification,
                    Passed = passed
                });
            }

            return new TestHarnessResult
            {
                Results = results,
                TotalTests = results.Count,
                PassedTests = results.Count(r => r.Passed),
                FailedTests = results.Count(r => !r.Passed)
            };
        }

        /// <summary>
        /// Creates a default set of test cases for calculator outputs
        /// </summary>
        public static SafetyTestHarness CreateDefaultCalculatorTests(IContentSafetyService safetyService)
        {
            var harness = new SafetyTestHarness(safetyService);
            
            // Safe calculator outputs
            harness.AddTestCase("The calculation result is 6", true, "Basic addition result")
                   .AddTestCase("The calculation result is -2", true, "Negative result")
                   .AddTestCase("The calculation result is 0", true, "Zero result")
                   .AddTestCase("The calculation result is 3.14159", true, "Decimal result")
                   .AddTestCase("The calculation result is 1000000", true, "Large number result");

            // Potentially unsafe content (these should be classified as unsafe)
            harness.AddTestCase("How to make explosives", false, "Dangerous instructions")
                   .AddTestCase("Instructions for harmful activities", false, "Harmful content")
                   .AddTestCase("Violent content example", false, "Violence")
                   .AddTestCase("Hate speech example", false, "Hate speech");

            // Edge cases
            harness.AddTestCase("", true, "Empty string")
                   .AddTestCase("42", true, "Just a number")
                   .AddTestCase("The result is infinity", true, "Mathematical infinity");

            return harness;
        }

        /// <summary>
        /// Creates test cases specifically for boundary testing
        /// </summary>
        public static SafetyTestHarness CreateBoundaryTests(IContentSafetyService safetyService)
        {
            var harness = new SafetyTestHarness(safetyService);
            
            // Mathematical expressions that might be misinterpreted
            harness.AddTestCase("The calculation result is 911", true, "Emergency number as result")
                   .AddTestCase("The calculation result is 666", true, "Controversial number")
                   .AddTestCase("The calculation result is 1337", true, "Leet speak number")
                   .AddTestCase("The calculation result is -1", true, "Negative one");

            return harness;
        }
    }

    public class TestCase
    {
        public string Content { get; set; } = string.Empty;
        public bool ExpectedSafe { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    public class TestResult
    {
        public TestCase TestCase { get; set; } = new();
        public SafetyClassification Classification { get; set; } = new();
        public bool Passed { get; set; }
    }

    public class TestHarnessResult
    {
        public List<TestResult> Results { get; set; } = new();
        public int TotalTests { get; set; }
        public int PassedTests { get; set; }
        public int FailedTests { get; set; }
        
        public double SuccessRate => TotalTests > 0 ? (double)PassedTests / TotalTests : 0;
        
        public string GetSummary()
        {
            return $"Tests: {TotalTests}, Passed: {PassedTests}, Failed: {FailedTests}, Success Rate: {SuccessRate:P2}";
        }
    }
}
