namespace Calculators.ContentSafety
{
    /// <summary>
    /// A null implementation of IContentSafetyService that always returns safe classifications.
    /// Used as a default implementation when no specific safety service is provided.
    /// </summary>
    public class NullContentSafetyService : IContentSafetyService
    {
        /// <summary>
        /// Always returns a safe classification for any content
        /// </summary>
        /// <param name="content">The content to validate</param>
        /// <returns>A safe classification result</returns>
        public Task<SafetyClassification> ValidateContentAsync(string content)
        {
            return Task.FromResult(SafetyClassification.Safe(content));
        }

        /// <summary>
        /// Always returns safe classifications for all content
        /// </summary>
        /// <param name="contents">The contents to validate</param>
        /// <returns>List of safe classification results</returns>
        public Task<List<SafetyClassification>> ValidateContentsAsync(IEnumerable<string> contents)
        {
            var results = contents.Select(content => SafetyClassification.Safe(content)).ToList();
            return Task.FromResult(results);
        }
    }
}
