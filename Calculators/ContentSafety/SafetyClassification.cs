namespace Calculators.ContentSafety
{
    /// <summary>
    /// Represents the result of a content safety classification
    /// </summary>
    public class SafetyClassification
    {
        public bool IsSafe { get; set; }
        public string? Category { get; set; }
        public string Content { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        
        public static SafetyClassification Safe(string content)
        {
            return new SafetyClassification
            {
                IsSafe = true,
                Content = content
            };
        }
        
        public static SafetyClassification Unsafe(string content, string? category = null)
        {
            return new SafetyClassification
            {
                IsSafe = false,
                Content = content,
                Category = category
            };
        }
    }
}
