using System.Net.Http.Json;
using System.Text.Json;

namespace Calculators.ContentSafety
{
    /// <summary>
    /// Content safety service that uses Ollama with Llama Guard for validation
    /// </summary>
    public class OllamaContentSafetyService : IContentSafetyService
    {
        private readonly HttpClient _httpClient;
        private readonly string _ollamaBaseUrl;
        private readonly string _modelName;

        public OllamaContentSafetyService(HttpClient httpClient, string ollamaBaseUrl = "http://localhost:11434", string modelName = "llama-guard3:8b")
        {
            _httpClient = httpClient;
            _ollamaBaseUrl = ollamaBaseUrl;
            _modelName = modelName;
        }

        public async Task<SafetyClassification> ValidateContentAsync(string content)
        {
            try
            {
                var prompt = $"Please classify this content: '{content}'";
                var response = await CallOllamaAsync(prompt);
                
                return ParseSafetyResponse(content, response);
            }
            catch (Exception ex)
            {
                // In case of error, default to unsafe for security
                return SafetyClassification.Unsafe(content, $"Error: {ex.Message}");
            }
        }

        public async Task<List<SafetyClassification>> ValidateContentsAsync(IEnumerable<string> contents)
        {
            var tasks = contents.Select(ValidateContentAsync);
            var results = await Task.WhenAll(tasks);
            return results.ToList();
        }

        private async Task<string> CallOllamaAsync(string prompt)
        {
            var requestBody = new
            {
                model = _modelName,
                prompt = prompt,
                stream = false
            };

            var response = await _httpClient.PostAsJsonAsync($"{_ollamaBaseUrl}/api/generate", requestBody);
            response.EnsureSuccessStatusCode();

            var responseContent = await response.Content.ReadAsStringAsync();
            var jsonResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);
            
            return jsonResponse.GetProperty("response").GetString() ?? string.Empty;
        }

        private static SafetyClassification ParseSafetyResponse(string content, string response)
        {
            var cleanResponse = response.Trim().ToLowerInvariant();
            
            if (cleanResponse.Contains("safe") && !cleanResponse.Contains("unsafe"))
            {
                return SafetyClassification.Safe(content);
            }
            else if (cleanResponse.Contains("unsafe"))
            {
                // Extract category if present (e.g., "unsafe\nS9")
                var lines = response.Split('\n', StringSplitOptions.RemoveEmptyEntries);
                var category = lines.Length > 1 ? lines[1].Trim() : null;
                return SafetyClassification.Unsafe(content, category);
            }
            else
            {
                // If response is unclear, default to unsafe for security
                return SafetyClassification.Unsafe(content, "Unclear response");
            }
        }
    }
}
