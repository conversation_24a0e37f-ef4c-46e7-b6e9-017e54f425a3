namespace Calculators.ContentSafety
{
    /// <summary>
    /// Exception thrown when content is classified as unsafe
    /// </summary>
    public class UnsafeContentException : Exception
    {
        public SafetyClassification SafetyClassification { get; }

        public UnsafeContentException(string message, SafetyClassification safetyClassification) 
            : base(message)
        {
            SafetyClassification = safetyClassification;
        }

        public UnsafeContentException(string message, SafetyClassification safetyClassification, Exception innerException) 
            : base(message, innerException)
        {
            SafetyClassification = safetyClassification;
        }
    }
}
