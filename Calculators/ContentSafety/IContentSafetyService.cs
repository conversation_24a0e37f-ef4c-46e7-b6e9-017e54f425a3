namespace Calculators.ContentSafety
{
    /// <summary>
    /// Interface for content safety validation services
    /// </summary>
    public interface IContentSafetyService
    {
        /// <summary>
        /// Validates content for safety using Llama Guard
        /// </summary>
        /// <param name="content">The content to validate</param>
        /// <returns>Safety classification result</returns>
        Task<SafetyClassification> ValidateContentAsync(string content);
        
        /// <summary>
        /// Validates multiple pieces of content for safety
        /// </summary>
        /// <param name="contents">The contents to validate</param>
        /// <returns>List of safety classification results</returns>
        Task<List<SafetyClassification>> ValidateContentsAsync(IEnumerable<string> contents);
    }
}
